'use client';

import { useState, useEffect } from 'react';
import { MdCheck, MdClose, MdVisibility, MdDownload, Md<PERSON>erson } from 'react-icons/md';

const DoctorVerification = () => {
  const [pendingDoctors, setPendingDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [verificationNotes, setVerificationNotes] = useState('');

  useEffect(() => {
    fetchPendingDoctors();
  }, []);

  const fetchPendingDoctors = async () => {
    try {
      const response = await fetch('/api/admin/pending-doctors');
      const data = await response.json();
      setPendingDoctors(data.doctors || []);
    } catch (error) {
      console.error('Error fetching pending doctors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async (doctorId, status, notes = '') => {
    try {
      const response = await fetch('/api/admin/verify-doctor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doctorId,
          status,
          notes
        }),
      });

      if (response.ok) {
        // Remove doctor from pending list
        setPendingDoctors(prev => prev.filter(doc => doc._id !== doctorId));
        setSelectedDoctor(null);
        setVerificationNotes('');
      }
    } catch (error) {
      console.error('Error updating verification status:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6B7AFF]"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Doctor Verification</h2>
        <div className="bg-[#FFE27A]/10 border border-[#FFE27A]/20 rounded-xl px-4 py-2">
          <span className="text-[#B8860B] font-medium">{pendingDoctors.length} Pending</span>
        </div>
      </div>

      {pendingDoctors.length === 0 ? (
        <div className="text-center py-12">
          <MdCheck className="w-16 h-16 text-[#56E0A0] mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">All Caught Up!</h3>
          <p className="text-gray-600">No doctors pending verification at the moment.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Doctor List */}
          <div className="space-y-4">
            {pendingDoctors.map((doctor) => (
              <div
                key={doctor._id}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all
                  ${selectedDoctor?._id === doctor._id
                    ? 'border-[#6B7AFF] bg-[#6B7AFF]/5'
                    : 'border-[#DDE1EC] hover:border-[#6B7AFF]/50'}`}
                onClick={() => setSelectedDoctor(doctor)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-[#6B7AFF]/10 rounded-xl flex items-center justify-center">
                      <MdPerson className="w-6 h-6 text-[#6B7AFF]" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{doctor.fullName}</h3>
                      <p className="text-sm text-gray-600">{doctor.email}</p>
                      <p className="text-sm text-[#6B7AFF]">
                        {doctor.doctorProfile?.specialization}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-500">
                      Registered {formatDate(doctor.createdAt)}
                    </div>
                    <div className="text-xs text-[#FFE27A] bg-[#FFE27A]/10 px-2 py-1 rounded mt-1">
                      Pending
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Doctor Details */}
          <div className="lg:sticky lg:top-6">
            {selectedDoctor ? (
              <div className="bg-white border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Verification Details
                </h3>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Personal Information</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Name:</span>
                        <p className="font-medium">{selectedDoctor.fullName}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Email:</span>
                        <p className="font-medium">{selectedDoctor.email}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Phone:</span>
                        <p className="font-medium">{selectedDoctor.phone}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Gender:</span>
                        <p className="font-medium capitalize">{selectedDoctor.gender}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Professional Information</h4>
                    <div className="grid grid-cols-1 gap-3 text-sm">
                      <div>
                        <span className="text-gray-600">Specialization:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.specialization}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">License Number:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.licenseNumber}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">License State:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.licenseState}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">NPI Number:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.npiNumber || 'Not provided'}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Experience:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.experience}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Medical School:</span>
                        <p className="font-medium">{selectedDoctor.doctorProfile?.education?.medicalSchool || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Verification Notes</h4>
                    <textarea
                      value={verificationNotes}
                      onChange={(e) => setVerificationNotes(e.target.value)}
                      className="w-full px-4 py-3 rounded-xl border-2 border-[#DDE1EC] 
                               bg-white focus:border-[#6B7AFF] focus:ring-4 focus:ring-[#6B7AFF]/10 
                               transition-all text-gray-900"
                      rows={3}
                      placeholder="Add verification notes (optional)..."
                    />
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <button
                      onClick={() => handleVerification(selectedDoctor._id, 'approved', verificationNotes)}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-3 
                               bg-[#56E0A0] text-white rounded-xl font-medium 
                               hover:bg-[#56E0A0]/90 transition-all"
                    >
                      <MdCheck className="w-5 h-5" />
                      Approve
                    </button>
                    <button
                      onClick={() => handleVerification(selectedDoctor._id, 'rejected', verificationNotes)}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-3 
                               bg-[#FF5656] text-white rounded-xl font-medium 
                               hover:bg-[#FF5656]/90 transition-all"
                    >
                      <MdClose className="w-5 h-5" />
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 border border-[#DDE1EC] rounded-xl p-8 text-center">
                <MdVisibility className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="font-medium text-gray-900 mb-2">Select a Doctor</h3>
                <p className="text-gray-600">Choose a doctor from the list to view verification details</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DoctorVerification;
