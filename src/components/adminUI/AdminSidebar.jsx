'use client';
import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  MdDashboard, MdPeople, MdVerifiedUser, MdPersonAdd, MdArticle, 
  MdAnalytics, MdSettings, MdSupport, MdNotifications, MdLogout,
  MdMenu, MdClose
} from 'react-icons/md';
import { 
  RiAdminLine, RiUserHeartLine, RiStethoscopeLine, RiShieldCheckLine,
  RiFileTextLine, RiBarChartLine, RiSettingsLine, RiCustomerServiceLine
} from 'react-icons/ri';

const AdminSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  const navigationItems = [
    {
      title: 'Dashboard',
      href: '/admin',
      icon: MdDashboard,
      description: 'System overview'
    },
    {
      title: 'User Management',
      href: '/admin/users',
      icon: MdPeople,
      description: 'Manage all users'
    },
    {
      title: 'Doctor Verification',
      href: '/admin/doctors',
      icon: RiStethoscopeLine,
      description: 'Verify doctor credentials'
    },
    {
      title: 'Patient Management',
      href: '/admin/patients',
      icon: RiUserHeartLine,
      description: 'Patient oversight'
    },
    {
      title: 'Content Management',
      href: '/admin/content',
      icon: RiFileTextLine,
      description: 'Manage content'
    },
    {
      title: 'Analytics & Reports',
      href: '/admin/analytics',
      icon: RiBarChartLine,
      description: 'System analytics'
    },
    {
      title: 'System Settings',
      href: '/admin/settings',
      icon: RiSettingsLine,
      description: 'Platform configuration'
    },
    {
      title: 'Support Center',
      href: '/admin/support',
      icon: RiCustomerServiceLine,
      description: 'Help desk & support'
    }
  ];

  const isActive = (href) => {
    if (href === '/admin') {
      return pathname === '/admin';
    }
    return pathname.startsWith(href);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-lg border border-[#DDE1EC]"
      >
        {isCollapsed ? <MdMenu className="w-6 h-6" /> : <MdClose className="w-6 h-6" />}
      </button>

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white border-r border-[#DDE1EC] 
        transform transition-transform duration-300 ease-in-out
        ${isCollapsed ? '-translate-x-full lg:translate-x-0' : 'translate-x-0'}
        lg:translate-x-0 flex flex-col
      `}>
        {/* Header */}
        <div className="p-6 border-b border-[#DDE1EC]">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-[#6B7AFF] to-[#8B6DFF] rounded-lg flex items-center justify-center">
              <RiAdminLine className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="font-bold text-[#1C243C]">Admin Portal</h1>
              <p className="text-xs text-[#8F96AA]">Wound Care Platform</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`
                  flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200
                  ${active 
                    ? 'bg-[#6B7AFF]/10 text-[#6B7AFF] border border-[#6B7AFF]/20' 
                    : 'text-[#8F96AA] hover:bg-[#F8F9FF] hover:text-[#1C243C]'
                  }
                `}
                onClick={() => setIsCollapsed(true)}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{item.title}</p>
                  <p className="text-xs opacity-75 truncate">{item.description}</p>
                </div>
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-[#DDE1EC]">
          <div className="flex items-center gap-3 px-3 py-2 text-[#8F96AA]">
            <RiShieldCheckLine className="w-5 h-5" />
            <div className="flex-1">
              <p className="text-sm font-medium">Admin User</p>
              <p className="text-xs">System Administrator</p>
            </div>
          </div>
          
          <button className="w-full mt-3 flex items-center gap-3 px-3 py-2 text-[#FF5656] hover:bg-[#FF5656]/5 rounded-lg transition-colors">
            <MdLogout className="w-5 h-5" />
            <span className="font-medium">Sign Out</span>
          </button>
        </div>
      </div>

      {/* Mobile Overlay */}
      {!isCollapsed && (
        <div 
          className="lg:hidden fixed inset-0 bg-black/50 z-30"
          onClick={() => setIsCollapsed(true)}
        />
      )}
    </>
  );
};

export default AdminSidebar;
