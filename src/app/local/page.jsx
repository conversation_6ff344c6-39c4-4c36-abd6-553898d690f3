import Link from 'next/link';
import Image from "next/image";
import { MdShield, MdAnalytics, MdArrowForward, MdCheck, MdStar, MdPlayArrow, MdTrendingUp, MdSecurity, MdSpeed } from 'react-icons/md';
import { RiFirstAidKitLine, RiTeamLine, RiStethoscopeLine, RiHeartPulseLine, RiUserHeartLine, RiAwardLine, RiTimeLine } from 'react-icons/ri';
import { HiOutlineChartBar, HiOutlineUsers, HiOutlineShieldCheck } from 'react-icons/hi';
import Footer from '@/components/publicUi/Footer';

const benefits = [
  {
    icon: RiHeartPulseLine,
    title: 'Improved Patient Outcomes',
    description: 'Track healing progress with precision',
    color: '#8B6DFF',
  },
  {
    icon: RiStethoscopeLine,
    title: 'Clinical Excellence',
    description: 'Evidence-based wound care protocols',
    color: '#5698FF',
  },
  {
    icon: MdAnalytics,
    title: 'Advanced Analytics',
    description: 'Comprehensive healing metrics and progress visualization',
    color: '#5698FF',
  },
  {
    icon: MdShield,
    title: 'HIPAA Compliant',
    description: 'Secure, encrypted, and compliant with healthcare standards',
    color: '#56E0A0',
  },
  {
    icon: RiTeamLine,
    title: 'Team Collaboration',
    description: 'Seamless communication between healthcare providers',
    color: '#6B7AFF',
  }
];
const features = [
  {
    icon: RiFirstAidKitLine,
    title: 'AI-Driven Assessments',
    description: 'Automated wound assessments using advanced AI algorithms',
    bgColor: 'bg-[#6B7AFF]/10',
    color: '#6B7AFF',
  },
  {
    icon: MdAnalytics,
    title: 'Real-Time Analytics',
    description: 'Track healing progress with comprehensive metrics',
    bgColor: 'bg-[#8B6DFF]/10',
    color: '#8B6DFF',
  },
  {
    icon: MdShield,
    title: 'HIPAA Compliant',
    description: 'Secure and compliant platform for healthcare professionals',
    bgColor: 'bg-[#56E0A0]/10',
    color: '#56E0A0',
  },
  {
    icon: RiTeamLine,
    title: 'Collaborative Care',
    description: 'Enhance teamwork with real-time communication tools',
    bgColor: 'bg-[#5698FF]/10',
    color: '#5698FF',
  },
];

export default function Home() {
  return (
    <>
      <div className="relative">
        {/* Hero Section */}
        <section className="relative pt-20 pb-32 overflow-hidden bg-white">
          <div className="absolute inset-0 bg-gradient-to-br from-[#F8F9FF] via-white to-[#F0F4FF]"/>
          <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5"/>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-4xl mx-auto">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-[#6B7AFF]/10 backdrop-blur-sm mb-8">
                <span className="flex h-2 w-2 rounded-full bg-[#56E0A0] mr-2"/>
                <span className="text-sm text-[#6B7AFF] font-medium">Trusted by 50,000+ Healthcare Professionals</span>
              </div>
              <h1 className="text-5xl lg:text-7xl font-bold text-[#1C243C] mb-8 leading-tight">
                Why Hire People <br/>
                <span className="bg-gradient-to-r from-[#6B7AFF] to-[#8B6DFF] bg-clip-text text-transparent">
                  When You Can Hire AI?
                </span>
              </h1>
              <p className="text-xl text-[#8F96AA] mb-12 leading-relaxed max-w-3xl mx-auto">
                Cut Costs, Save Time, Let AI Do the Work! Revolutionary wound care platform that combines
                artificial intelligence with clinical expertise.
              </p>
              <div className="flex flex-wrap justify-center gap-4 mb-16">
                <Link href="/auth/register"
                      className="group bg-[#1C243C] text-white px-8 py-4 rounded-xl font-medium
                               hover:bg-[#2A3441] hover:shadow-xl transition-all">
                  <MdPlayArrow className="inline mr-2"/>
                  Hire Your AI Expert
                </Link>
                <Link href="#demo"
                      className="px-8 py-4 rounded-xl text-[#1C243C] border border-[#DDE1EC]
                               hover:bg-[#F8F9FF] transition-all">
                  Watch Demo
                </Link>
              </div>

              {/* Hero Image with Professional Team */}
              <div className="relative">
                <div className="bg-gradient-to-br from-[#FF6B9D] via-[#C471ED] to-[#12C2E9] rounded-3xl p-8 shadow-2xl">
                  <div className="flex justify-center items-center gap-8 flex-wrap">
                    {/* Professional Avatars */}
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <span className="text-white font-bold">👩‍⚖️</span>
                      </div>
                      <div className="text-white text-left">
                        <div className="font-medium">Lawyer</div>
                        <div className="text-sm opacity-80">Legal Expert</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <span className="text-white font-bold">👨‍💼</span>
                      </div>
                      <div className="text-white text-left">
                        <div className="font-medium">Civil Engineer</div>
                        <div className="text-sm opacity-80">Infrastructure</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <RiStethoscopeLine className="text-white text-2xl"/>
                      </div>
                      <div className="text-white text-left">
                        <div className="font-medium">Doctor</div>
                        <div className="text-sm opacity-80">Medical Expert</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <span className="text-white font-bold">👨‍💻</span>
                      </div>
                      <div className="text-white text-left">
                        <div className="font-medium">Designer</div>
                        <div className="text-sm opacity-80">Creative Expert</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <span className="text-white font-bold">🏆</span>
                      </div>
                      <div className="text-white text-left">
                        <div className="font-medium">37 Professions to Hire</div>
                        <div className="text-sm opacity-80">AI Specialists</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Trusted by logos */}
                <div className="mt-16 text-center">
                  <p className="text-sm text-[#8F96AA] mb-8">Relied upon by the world's best product teams.</p>
                  <div className="flex justify-center items-center gap-8 flex-wrap opacity-60">
                    <div className="text-2xl font-bold text-[#1C243C]">Nietzsche</div>
                    <div className="text-2xl font-bold text-[#1C243C]">FocalPoint</div>
                    <div className="text-2xl font-bold text-[#1C243C]">Command+R</div>
                    <div className="text-2xl font-bold text-[#1C243C]">GlobalBank</div>
                    <div className="text-2xl font-bold text-[#1C243C]">Alt+Shift</div>
                    <div className="text-2xl font-bold text-[#1C243C]">Luminous</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features that help you grow Section */}
        <section className="py-20 bg-white relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-[#1C243C] mb-4">
                Features that help you grow
              </h2>
              <p className="text-[#8F96AA] max-w-2xl mx-auto">
                Experience enhanced efficiency, heightened productivity.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left side - Feature cards */}
              <div className="space-y-8">
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-[#F8F9FF] p-6 rounded-2xl">
                    <div className="w-12 h-12 bg-[#6B7AFF]/10 rounded-xl flex items-center justify-center mb-4">
                      <RiFirstAidKitLine className="w-6 h-6 text-[#6B7AFF]"/>
                    </div>
                    <h3 className="font-semibold text-[#1C243C] mb-2">Where your money go?</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-[#8F96AA]">Food and Drinks</span>
                        <span className="text-[#1C243C]">$847.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#8F96AA]">Shopping</span>
                        <span className="text-[#1C243C]">$1,574.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#8F96AA]">Housing</span>
                        <span className="text-[#1C243C]">$928.00</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#F8F9FF] p-6 rounded-2xl">
                    <h3 className="font-semibold text-[#1C243C] mb-4">Visual presentation</h3>
                    <p className="text-sm text-[#8F96AA] mb-4">Experience enhanced efficiency, heightened productivity.</p>
                    <div className="bg-white p-4 rounded-xl">
                      <div className="text-2xl font-bold text-[#1C243C]">$85,056</div>
                      <div className="text-sm text-[#56E0A0]">+$12.00</div>
                      <div className="mt-2 h-16 bg-gradient-to-r from-[#56E0A0] to-[#6B7AFF] rounded opacity-20"></div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-6">
                  <div className="bg-[#F8F9FF] p-6 rounded-2xl text-center">
                    <h4 className="font-semibold text-[#1C243C] mb-2">Connect with 200+ apps natively</h4>
                    <p className="text-sm text-[#8F96AA]">Experience enhanced efficiency, heightened productivity.</p>
                  </div>

                  <div className="bg-[#F8F9FF] p-6 rounded-2xl text-center">
                    <h4 className="font-semibold text-[#1C243C] mb-2">Get exact data of expenses</h4>
                    <p className="text-sm text-[#8F96AA]">Experience enhanced efficiency, heightened productivity.</p>
                  </div>

                  <div className="bg-[#F8F9FF] p-6 rounded-2xl text-center">
                    <h4 className="font-semibold text-[#1C243C] mb-2">Get Notified Instantly</h4>
                    <p className="text-sm text-[#8F96AA]">Experience enhanced efficiency, heightened productivity.</p>
                  </div>
                </div>
              </div>

              {/* Right side - Analytics dashboard */}
              <div className="bg-[#F8F9FF] p-8 rounded-3xl">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-[#FF6B9D] rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">T</span>
                    </div>
                    <div>
                      <div className="font-medium text-[#1C243C]">Transportation</div>
                      <div className="text-sm text-[#8F96AA]">$25.00</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-[#56E0A0] rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">S</span>
                    </div>
                    <div>
                      <div className="font-medium text-[#1C243C]">Shopping</div>
                      <div className="text-sm text-[#8F96AA]">$574.00</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-[#FFB547] rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">E</span>
                    </div>
                    <div>
                      <div className="font-medium text-[#1C243C]">Entertainment</div>
                      <div className="text-sm text-[#8F96AA]">$124.00</div>
                    </div>
                  </div>

                  <div className="mt-8">
                    <h3 className="font-semibold text-[#1C243C] mb-4">Expenses</h3>
                    <div className="text-sm text-[#8F96AA] mb-2">01 - 25 March, 2023</div>
                    <div className="h-32 bg-white rounded-xl p-4 flex items-end justify-between">
                      {[40, 60, 30, 80, 50, 70, 45].map((height, i) => (
                        <div key={i} className={`w-4 bg-[#6B7AFF] rounded-t`} style={{height: `${height}%`}}></div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-xl">
                    <div className="text-sm text-[#8F96AA] mb-2">Trusted by</div>
                    <div className="text-2xl font-bold text-[#6B7AFF] mb-2">24,000+ Professionals</div>
                    <div className="flex -space-x-2">
                      {[1,2,3].map((i) => (
                        <div key={i} className="w-8 h-8 rounded-full bg-[#6B7AFF]/20 border-2 border-white"></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials from our valued clients */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-[#1C243C] mb-4">
                Testimonials from our <br/>
                valued clients.
              </h2>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {/* Slack Testimonial */}
              <div className="bg-white p-8 rounded-2xl border border-[#DDE1EC] shadow-sm">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-[#4A154B] rounded flex items-center justify-center">
                    <span className="text-white text-sm font-bold">#</span>
                  </div>
                  <span className="font-bold text-[#1C243C]">slack</span>
                </div>
                <blockquote className="text-[#1C243C] mb-6 leading-relaxed">
                  "Hive Payment App has revolutionized the way I manage my expenses. Whether I'm on a business trip or exploring"
                </blockquote>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-[#1C243C]">Robert Fox</div>
                    <div className="text-sm text-[#8F96AA]">Legal Advisor</div>
                  </div>
                  <div className="flex gap-1 text-[#56E0A0]">
                    {[...Array(5)].map((_, i) => (
                      <MdStar key={i} className="w-4 h-4"/>
                    ))}
                  </div>
                </div>
                <div className="mt-4 text-sm text-[#8F96AA]">5.00 • Awesome</div>
              </div>

              {/* PayPal Testimonial */}
              <div className="bg-white p-8 rounded-2xl border border-[#DDE1EC] shadow-sm">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-[#0070BA] rounded flex items-center justify-center">
                    <span className="text-white text-sm font-bold">P</span>
                  </div>
                  <span className="font-bold text-[#1C243C]">PayPal</span>
                </div>
                <blockquote className="text-[#1C243C] mb-6 leading-relaxed">
                  "Hive Payment App has revolutionized the way I manage my expenses. Whether I'm on a business trip or exploring"
                </blockquote>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-[#1C243C]">Robert Fox</div>
                    <div className="text-sm text-[#8F96AA]">Legal Advisor</div>
                  </div>
                  <div className="flex gap-1 text-[#56E0A0]">
                    {[...Array(5)].map((_, i) => (
                      <MdStar key={i} className="w-4 h-4"/>
                    ))}
                  </div>
                </div>
                <div className="mt-4 text-sm text-[#8F96AA]">5.00 • Awesome</div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20 bg-[#F8F9FF]">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-4">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-[#6B7AFF]/10 text-[#6B7AFF] text-sm mb-4">
                Pricing Plan
              </div>
            </div>

            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-[#1C243C] mb-4">
                Find the Right Plan for <br/>
                Your Organization
              </h2>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Standard Plan */}
              <div className="bg-white p-8 rounded-2xl border border-[#DDE1EC] relative">
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="text-xl font-bold text-[#1C243C]">Standard</h3>
                  <span className="bg-[#FFB547] text-white text-xs px-2 py-1 rounded-full">Most Popular</span>
                </div>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-[#1C243C]">$199</span>
                  <span className="text-[#8F96AA]">/month</span>
                </div>
                <p className="text-[#8F96AA] mb-6">
                  Get key community-building features, all in one place. Scale and community with workflows.
                </p>
                <button className="w-full bg-[#1C243C] text-white py-3 rounded-xl font-medium hover:bg-[#2A3441] transition-colors mb-6">
                  Try for free trial
                </button>
                <div>
                  <h4 className="font-semibold text-[#1C243C] mb-4">What's included</h4>
                  <ul className="space-y-3 text-sm text-[#8F96AA]">
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Advanced administration
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Unlimited projects creation
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Up to 1,000 members
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Priority support
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Enhanced email sending
                    </li>
                  </ul>
                </div>
              </div>

              {/* Growth Plan */}
              <div className="bg-white p-8 rounded-2xl border border-[#DDE1EC] relative">
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="text-xl font-bold text-[#1C243C]">Growth</h3>
                  <span className="bg-[#FFB547] text-white text-xs px-2 py-1 rounded-full">Most Popular</span>
                </div>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-[#1C243C]">$275</span>
                  <span className="text-[#8F96AA]">/month</span>
                </div>
                <p className="text-[#8F96AA] mb-6">
                  Get key community-building features, all in one place. Scale and community with workflows.
                </p>
                <button className="w-full bg-[#1C243C] text-white py-3 rounded-xl font-medium hover:bg-[#2A3441] transition-colors mb-6">
                  Try for free trial
                </button>
                <div>
                  <h4 className="font-semibold text-[#1C243C] mb-4">What's included</h4>
                  <ul className="space-y-3 text-sm text-[#8F96AA]">
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Advanced data enrichment
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Unlimited projects creation
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Up to 5,000 members
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Priority support
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Enhanced email sending
                    </li>
                  </ul>
                </div>
              </div>

              {/* Enterprise Plan */}
              <div className="bg-white p-8 rounded-2xl border border-[#DDE1EC] relative">
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="text-xl font-bold text-[#1C243C]">Enterprise</h3>
                  <span className="bg-[#FFB547] text-white text-xs px-2 py-1 rounded-full">Most Popular</span>
                </div>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-[#1C243C]">$389</span>
                  <span className="text-[#8F96AA]">/month</span>
                </div>
                <p className="text-[#8F96AA] mb-6">
                  Get key community-building features, all in one place. Scale and community with workflows.
                </p>
                <button className="w-full bg-[#1C243C] text-white py-3 rounded-xl font-medium hover:bg-[#2A3441] transition-colors mb-6">
                  Try for free trial
                </button>
                <div>
                  <h4 className="font-semibold text-[#1C243C] mb-4">What's included</h4>
                  <ul className="space-y-3 text-sm text-[#8F96AA]">
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Advanced data enrichment
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Unlimited projects creation
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Up to 5,000 members
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Priority support
                    </li>
                    <li className="flex items-center gap-2">
                      <MdCheck className="w-4 h-4 text-[#56E0A0]"/>
                      Enhanced email sending
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-4">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-[#6B7AFF]/10 text-[#6B7AFF] text-sm mb-4">
                Frequently Asked
              </div>
            </div>

            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-[#1C243C] mb-4">
                We'll be There When <br/>
                You Need us
              </h2>
            </div>

            <div className="space-y-6">
              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">How can marketing automation benefit my business?</h3>
                <p className="text-[#8F96AA]">
                  Marketing automation can streamline your marketing efforts by automating repetitive tasks,
                  nurturing leads, and improving the personalized experience. It enhances efficiency, improves targeting,
                  and drives higher ROI on your marketing campaigns.
                </p>
              </div>

              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">How can marketing automation help me save time and resources?</h3>
                <p className="text-[#8F96AA]">
                  Remember to tailor the above to the specific benefits and functionalities of marketing
                  automation for your target audience and industry.
                </p>
              </div>

              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">What is marketing automation, and how does it work?</h3>
              </div>

              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">How do I measure the ROI of implementing a marketing automation strategy?</h3>
              </div>

              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">Can marketing automation be personalized to suit my business's unique needs?</h3>
              </div>

              <div className="border border-[#DDE1EC] rounded-xl p-6">
                <h3 className="font-semibold text-[#1C243C] mb-2">What types of tasks can be automated using marketing automation software?</h3>
              </div>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-[#1C243C] via-[#2A3441] to-[#1C243C]">
          <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5"/>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                Book Your Appointment Now
              </h2>
              <p className="text-white/80 mb-8 max-w-2xl mx-auto">
                Ready to transform your wound care practice? Schedule a personalized demo
                and see how our AI-powered platform can revolutionize your patient outcomes.
              </p>
              <Link
                href="/auth/register"
                className="inline-flex items-center bg-white text-[#1C243C] px-8 py-4 rounded-xl font-medium
                         hover:bg-gray-100 transition-all"
              >
                <MdPlayArrow className="mr-2"/>
                Get Started Now
              </Link>
            </div>
          </div>
        </section>
      </div>  
    </>
  );
}
