'use client';
import { useState } from 'react';
import Link from 'next/link';
import {
  MdTrendingUp, MdTrendingDown, MdWarning, MdCheckCircle,
  MdPeople, MdVerifiedUser, MdNotifications, MdSecurity,
  MdAnalytics, MdRefresh, MdMoreVert
} from 'react-icons/md';
import {
  RiUserHeartLine, RiStethoscopeLine, RiAlarmWarningLine,
  RiShieldCheckLine, RiTimeLine, RiBarChartLine, RiFileTextLine
} from 'react-icons/ri';

export default function AdminDashboard() {
  const [refreshing, setRefreshing] = useState(false);

  // System metrics data
  const systemMetrics = [
    {
      title: 'Total Users',
      value: '2,847',
      change: '+12%',
      trend: 'up',
      icon: MdPeople,
      color: 'text-[#6B7AFF]',
      bgColor: 'bg-[#6B7AFF]/10',
      description: 'Active platform users'
    },
    {
      title: 'Pending Approvals',
      value: '23',
      change: '+5',
      trend: 'up',
      icon: MdVerifiedUser,
      color: 'text-[#FFE27A]',
      bgColor: 'bg-[#FFE27A]/10',
      description: 'Doctor verifications needed'
    },
    {
      title: 'System Health',
      value: '99.8%',
      change: '+0.2%',
      trend: 'up',
      icon: MdCheckCircle,
      color: 'text-[#56E0A0]',
      bgColor: 'bg-[#56E0A0]/10',
      description: 'Platform uptime'
    },
    {
      title: 'Critical Alerts',
      value: '3',
      change: '-2',
      trend: 'down',
      icon: MdWarning,
      color: 'text-[#FF5656]',
      bgColor: 'bg-[#FF5656]/10',
      description: 'Require immediate attention'
    }
  ];

  // Quick actions
  const quickActions = [
    {
      title: 'Verify Doctors',
      description: '23 pending applications',
      href: '/admin/doctors',
      icon: RiStethoscopeLine,
      color: 'bg-[#6B7AFF]',
      urgent: true
    },
    {
      title: 'Review Reports',
      description: '8 new compliance reports',
      href: '/admin/analytics',
      icon: RiBarChartLine,
      color: 'bg-[#8B6DFF]',
      urgent: false
    },
    {
      title: 'Manage Content',
      description: '12 items awaiting approval',
      href: '/admin/content',
      icon: RiFileTextLine,
      color: 'bg-[#5698FF]',
      urgent: false
    },
    {
      title: 'System Settings',
      description: 'Configure platform features',
      href: '/admin/settings',
      icon: MdSecurity,
      color: 'bg-[#56E0A0]',
      urgent: false
    }
  ];

  // Recent activities
  const recentActivities = [
    {
      id: 1,
      type: 'user_verification',
      message: 'Dr. Sarah Johnson verified successfully',
      timestamp: '2 minutes ago',
      icon: RiStethoscopeLine,
      color: 'text-[#56E0A0]'
    },
    {
      id: 2,
      type: 'security_alert',
      message: 'Multiple failed login attempts detected',
      timestamp: '15 minutes ago',
      icon: RiAlarmWarningLine,
      color: 'text-[#FF5656]'
    },
    {
      id: 3,
      type: 'content_approval',
      message: 'New education content published',
      timestamp: '1 hour ago',
      icon: RiFileTextLine,
      color: 'text-[#6B7AFF]'
    },
    {
      id: 4,
      type: 'user_registration',
      message: '15 new patient registrations today',
      timestamp: '2 hours ago',
      icon: RiUserHeartLine,
      color: 'text-[#8B6DFF]'
    },
    {
      id: 5,
      type: 'system_update',
      message: 'Platform maintenance completed',
      timestamp: '4 hours ago',
      icon: RiShieldCheckLine,
      color: 'text-[#56E0A0]'
    }
  ];

  // Critical alerts
  const criticalAlerts = [
    {
      id: 1,
      title: 'High Server Load',
      description: 'Database response time above threshold',
      severity: 'critical',
      timestamp: '5 minutes ago'
    },
    {
      id: 2,
      title: 'Failed Backup',
      description: 'Automated backup process failed',
      severity: 'high',
      timestamp: '1 hour ago'
    },
    {
      id: 3,
      title: 'Security Scan Alert',
      description: 'Unusual access pattern detected',
      severity: 'medium',
      timestamp: '3 hours ago'
    }
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => setRefreshing(false), 1000);
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-[#1C243C]">Admin Dashboard</h1>
          <p className="text-[#8F96AA] mt-1">
            System overview and management center
          </p>
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2 px-4 py-2 border border-[#DDE1EC] rounded-lg hover:bg-[#F8F9FF] transition-colors disabled:opacity-50"
          >
            <MdRefresh className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>

          <Link
            href="/admin/settings"
            className="flex items-center gap-2 px-4 py-2 bg-[#6B7AFF] text-white rounded-lg hover:bg-[#506EFF] transition-colors"
          >
            <MdSecurity className="w-4 h-4" />
            System Settings
          </Link>
        </div>
      </div>

      {/* System Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {systemMetrics.map((metric, index) => (
          <div key={index} className="bg-white rounded-xl border border-[#DDE1EC] p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 rounded-lg ${metric.bgColor} flex items-center justify-center`}>
                <metric.icon className={`w-6 h-6 ${metric.color}`} />
              </div>
              {metric.trend === 'up' && <MdTrendingUp className="w-5 h-5 text-[#56E0A0]" />}
              {metric.trend === 'down' && <MdTrendingDown className="w-5 h-5 text-[#FF5656]" />}
            </div>
            <h3 className="text-sm text-[#8F96AA] mb-1">{metric.title}</h3>
            <div className="flex items-baseline gap-2 mb-2">
              <span className="text-2xl font-bold text-[#1C243C]">{metric.value}</span>
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-[#56E0A0]' : 'text-[#FF5656]'
              }`}>
                {metric.change}
              </span>
            </div>
            <p className="text-xs text-[#8F96AA]">{metric.description}</p>
          </div>
        ))}
      </div>

      {/* Critical Alerts */}
      {criticalAlerts.length > 0 && (
        <div className="bg-[#FF5656]/5 border border-[#FF5656]/20 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <RiAlarmWarningLine className="w-6 h-6 text-[#FF5656]" />
            <h2 className="text-lg font-semibold text-[#FF5656]">Critical System Alerts</h2>
          </div>
          <div className="space-y-3">
            {criticalAlerts.map((alert) => (
              <div key={alert.id} className="flex items-start justify-between p-3 bg-white rounded-lg border border-[#FF5656]/10">
                <div className="flex-1">
                  <h3 className="font-medium text-[#1C243C]">{alert.title}</h3>
                  <p className="text-sm text-[#8F96AA] mt-1">{alert.description}</p>
                  <span className="text-xs text-[#8F96AA]">{alert.timestamp}</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  alert.severity === 'critical' ? 'bg-[#FF5656]/10 text-[#FF5656]' :
                  alert.severity === 'high' ? 'bg-[#FFE27A]/10 text-[#FFE27A]' :
                  'bg-[#5698FF]/10 text-[#5698FF]'
                }`}>
                  {alert.severity}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white rounded-xl border border-[#DDE1EC] p-6">
          <h2 className="text-lg font-semibold text-[#1C243C] mb-4">Quick Actions</h2>
          <div className="space-y-3">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="flex items-center gap-4 p-4 rounded-lg border border-[#DDE1EC] hover:border-[#6B7AFF]/20 hover:bg-[#F8F9FF] transition-all group"
              >
                <div className={`w-12 h-12 rounded-lg ${action.color} flex items-center justify-center`}>
                  <action.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium text-[#1C243C] group-hover:text-[#6B7AFF]">{action.title}</h3>
                    {action.urgent && (
                      <span className="w-2 h-2 bg-[#FF5656] rounded-full animate-pulse"></span>
                    )}
                  </div>
                  <p className="text-sm text-[#8F96AA]">{action.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl border border-[#DDE1EC] p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-[#1C243C]">Recent Activity</h2>
            <Link href="/admin/analytics" className="text-sm text-[#6B7AFF] hover:text-[#506EFF]">
              View All
            </Link>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-lg bg-[#F8F9FF] flex items-center justify-center flex-shrink-0">
                  <activity.icon className={`w-4 h-4 ${activity.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-[#1C243C]">{activity.message}</p>
                  <p className="text-xs text-[#8F96AA] mt-1">{activity.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-xl border border-[#DDE1EC] p-6">
        <h2 className="text-lg font-semibold text-[#1C243C] mb-4">System Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-[#56E0A0]/10 rounded-full flex items-center justify-center mx-auto mb-3">
              <MdCheckCircle className="w-8 h-8 text-[#56E0A0]" />
            </div>
            <h3 className="font-medium text-[#1C243C]">Database</h3>
            <p className="text-sm text-[#56E0A0]">Operational</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-[#56E0A0]/10 rounded-full flex items-center justify-center mx-auto mb-3">
              <RiShieldCheckLine className="w-8 h-8 text-[#56E0A0]" />
            </div>
            <h3 className="font-medium text-[#1C243C]">Security</h3>
            <p className="text-sm text-[#56E0A0]">All Systems Secure</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-[#FFE27A]/10 rounded-full flex items-center justify-center mx-auto mb-3">
              <RiTimeLine className="w-8 h-8 text-[#FFE27A]" />
            </div>
            <h3 className="font-medium text-[#1C243C]">Performance</h3>
            <p className="text-sm text-[#FFE27A]">Monitoring</p>
          </div>
        </div>
      </div>
    </div>
  );
}
