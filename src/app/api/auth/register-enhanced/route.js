import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import UserModel from '@/models/user.model';
import { sendOTPEmail } from '@/utils/emailService';

export async function POST(request) {
  try {
    const body = await request.json();
    
    const {
      fullName,
      email,
      phone,
      password,
      gender,
      dateOfBirth,
      role,
      patientInfo,
      doctorInfo,
      consents
    } = body;

    // Validate required fields
    if (!fullName || !email || !phone || !password || !gender || !dateOfBirth || !role) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Validate phone format
    const phoneRegex = /^\(\d{3}\) \d{3}-\d{4}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json(
        { message: 'Invalid phone format. Use (*************' },
        { status: 400 }
      );
    }

    // Validate role-specific required fields
    if (role === 'doctor') {
      if (!doctorInfo?.specialization || !doctorInfo?.licenseNumber || !doctorInfo?.licenseState) {
        return NextResponse.json(
          { message: 'Missing required doctor information' },
          { status: 400 }
        );
      }
    }

    // Validate consents
    if (!consents?.terms || !consents?.hipaa) {
      return NextResponse.json(
        { message: 'Required consents must be accepted' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await UserModel.findByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Check if phone already exists
    const existingPhone = await UserModel.findByPhone(phone);
    if (existingPhone) {
      return NextResponse.json(
        { message: 'User with this phone number already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create user data structure
    const userData = {
      fullName,
      email,
      phone,
      password: hashedPassword,
      gender,
      dateOfBirth: new Date(dateOfBirth),
      role,
      isEmailVerified: false,
      isPhoneVerified: false,
      accountStatus: role === 'doctor' ? 'pending_verification' : 'active',
      otp,
      otpExpiry,
      consents: {
        terms: {
          accepted: consents.terms,
          timestamp: new Date(consents.timestamp)
        },
        hipaa: {
          accepted: consents.hipaa,
          timestamp: new Date(consents.timestamp)
        },
        marketing: {
          accepted: consents.marketing || false,
          timestamp: new Date(consents.timestamp)
        }
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add role-specific information
    if (role === 'patient' && patientInfo) {
      userData.patientProfile = {
        address: patientInfo.address,
        city: patientInfo.city,
        state: patientInfo.state,
        zipCode: patientInfo.zipCode,
        emergencyContact: patientInfo.emergencyContact,
        insurance: patientInfo.insurance,
        medical: patientInfo.medical,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    if (role === 'doctor' && doctorInfo) {
      userData.doctorProfile = {
        specialization: doctorInfo.specialization,
        licenseNumber: doctorInfo.licenseNumber,
        licenseState: doctorInfo.licenseState,
        npiNumber: doctorInfo.npiNumber,
        deaNumber: doctorInfo.deaNumber,
        education: doctorInfo.education,
        certifications: doctorInfo.certifications,
        affiliations: doctorInfo.affiliations,
        experience: doctorInfo.experience,
        practiceType: doctorInfo.practiceType,
        verificationStatus: 'pending',
        verificationDocuments: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    // Create user in database
    const newUser = await UserModel.create(userData);

    // Generate JWT token for OTP verification
    const otpToken = jwt.sign(
      { 
        userId: newUser._id,
        email: newUser.email,
        type: 'email_verification'
      },
      process.env.JWT_SECRET,
      { expiresIn: '10m' }
    );

    // Send OTP email
    try {
      await sendOTPEmail(email, otp, fullName, 'registration');
    } catch (emailError) {
      console.error('Failed to send OTP email:', emailError);
      // Don't fail registration if email fails, but log it
    }

    // Log registration event
    console.log(`Enhanced registration completed for ${email} (${role})`);

    // Return success response
    return NextResponse.json({
      message: 'Registration successful',
      data: {
        userId: newUser._id,
        email: newUser.email,
        role: newUser.role,
        accountStatus: newUser.accountStatus,
        requiresVerification: role === 'doctor',
        otpToken
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Enhanced registration error:', error);
    
    return NextResponse.json(
      { 
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
