import { NextResponse } from 'next/server';
import UserModel from '@/models/user.model';
import { sendEmail } from '@/utils/emailService';

export async function POST(request) {
  try {
    const { doctorId, status, notes } = await request.json();

    // Validate input
    if (!doctorId || !status || !['approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { message: 'Invalid verification data' },
        { status: 400 }
      );
    }

    // Find the doctor
    const doctor = await UserModel.findById(doctorId);
    if (!doctor || doctor.role !== 'doctor') {
      return NextResponse.json(
        { message: 'Doctor not found' },
        { status: 404 }
      );
    }

    // Update verification status
    const updateData = {
      'doctorProfile.verificationStatus': status,
      'doctorProfile.verificationDate': new Date(),
      'doctorProfile.verificationNotes': notes || '',
      accountStatus: status === 'approved' ? 'active' : 'suspended',
      updatedAt: new Date()
    };

    const updatedDoctor = await UserModel.findByIdAndUpdate(
      doctorId,
      updateData,
      { new: true }
    );

    // Send notification email
    try {
      const emailSubject = status === 'approved' 
        ? 'Account Verified - Welcome to AssistIQ'
        : 'Account Verification Update';

      const emailContent = status === 'approved'
        ? `
          <h2>Congratulations! Your account has been verified.</h2>
          <p>Dear Dr. ${doctor.fullName},</p>
          <p>Your professional credentials have been successfully verified. You now have full access to all AssistIQ features.</p>
          <p>You can now:</p>
          <ul>
            <li>Access advanced AI assistance tools</li>
            <li>Manage patient consultations</li>
            <li>Use professional analytics</li>
            <li>Collaborate with other healthcare professionals</li>
          </ul>
          <p>Welcome to the AssistIQ community!</p>
        `
        : `
          <h2>Account Verification Update</h2>
          <p>Dear Dr. ${doctor.fullName},</p>
          <p>We were unable to verify your professional credentials at this time.</p>
          ${notes ? `<p><strong>Notes:</strong> ${notes}</p>` : ''}
          <p>Please contact our support team for assistance with the verification process.</p>
        `;

      await sendEmail(doctor.email, emailSubject, emailContent);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail the verification if email fails
    }

    // Log verification action
    console.log(`Doctor verification ${status} for ${doctor.email} by admin`);

    return NextResponse.json({
      success: true,
      message: `Doctor ${status} successfully`,
      doctor: {
        id: updatedDoctor._id,
        name: updatedDoctor.fullName,
        email: updatedDoctor.email,
        status: updatedDoctor.accountStatus,
        verificationStatus: updatedDoctor.doctorProfile.verificationStatus
      }
    });

  } catch (error) {
    console.error('Error verifying doctor:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to update verification status',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
