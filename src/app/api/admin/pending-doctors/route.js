import { NextResponse } from 'next/server';
import UserModel from '@/models/user.model';
import { getServerSession } from 'next-auth';

export async function GET(request) {
  try {
    // Check if user is admin (you'll need to implement proper admin authentication)
    // const session = await getServerSession();
    // if (!session || session.user.role !== 'admin') {
    //   return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    // }

    // Find all doctors with pending verification status
    const pendingDoctors = await UserModel.findPendingDoctors();

    return NextResponse.json({
      success: true,
      doctors: pendingDoctors,
      count: pendingDoctors.length
    });

  } catch (error) {
    console.error('Error fetching pending doctors:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to fetch pending doctors',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
