import { SidebarProvider } from '@/context/SidebarContext';
import Doctor<PERSON><PERSON><PERSON> from '@/components/doctorUI/DoctorSidebar';
import DoctorW<PERSON>per from '@/components/doctorUI/DoctorWrapper';
import { getSession } from '@/utils/getSessions';
import { redirect } from 'next/navigation';

export default async function DoctorLayout({ children }) {
   const session = await getSession()
    //   if (session.isLogin == false || session.role != 'doctor') {
    //       redirect("/local")
    //   }
    return (
        <SidebarProvider>
            <DoctorSidebar />
            <DoctorWrapper>{children}</DoctorWrapper>
        </SidebarProvider>
    );
}
